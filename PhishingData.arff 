@relation 'Phishing-weka.filters.supervised.attribute.AttributeSelection-Eweka.attributeSelection.InfoGainAttributeEval-Sweka.attributeSelection.Ranker -T -1.7976931348623157E308 -N -1-weka.filters.unsupervised.attribute.Remove-R6,11-14,16-weka.filters.unsupervised.attribute.Remove-R2'

@attribute SFH {1,-1,0}
@attribute popUpWidnow {-1,0,1}
@attribute SSLfinal_State {1,-1,0}
@attribute Request_URL {-1,0,1}
@attribute URL_of_Anchor {-1,0,1}
@attribute web_traffic {1,0,-1}
@attribute URL_Length {1,-1,0}
@attribute age_of_domain {1,-1}
@attribute having_IP_Address {0,1}
@attribute Result {0,1,-1}

@data
1,-1,1,-1,-1,1,1,1,0,0
-1,-1,-1,-1,-1,0,1,1,1,1
1,-1,0,0,-1,0,-1,1,0,1
1,0,1,-1,-1,0,1,1,0,0
-1,-1,1,-1,0,0,-1,1,0,1
-1,-1,1,-1,-1,1,0,-1,0,1
1,-1,0,1,-1,0,0,1,0,-1
1,0,1,1,0,0,0,1,1,-1
-1,-1,0,-1,-1,-1,-1,1,0,0
-1,0,-1,-1,1,1,0,-1,0,1
-1,-1,0,-1,-1,1,-1,-1,0,1
1,0,1,1,1,-1,1,1,0,-1
1,-1,0,-1,1,0,1,1,0,1
1,0,1,0,-1,1,0,-1,0,-1
-1,-1,-1,1,-1,0,-1,1,0,1
0,0,-1,0,0,1,1,-1,1,1
-1,0,0,0,-1,1,-1,-1,0,1
1,1,1,-1,1,-1,1,1,0,-1
0,-1,-1,0,1,1,0,-1,0,1
1,-1,0,1,0,0,-1,1,0,1
0,-1,1,-1,-1,1,-1,1,0,1
1,0,-1,-1,1,1,-1,-1,0,-1
1,-1,0,-1,1,-1,0,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
1,0,-1,0,-1,1,-1,-1,0,-1
-1,-1,0,-1,1,-1,0,1,0,-1
0,0,1,1,1,0,0,1,0,-1
1,0,-1,1,1,0,0,-1,0,-1
1,0,1,0,-1,-1,0,1,0,-1
-1,-1,0,0,1,1,-1,-1,0,1
1,1,0,-1,1,1,0,-1,0,-1
1,1,1,-1,-1,0,0,1,0,-1
1,0,1,-1,-1,-1,0,1,0,-1
1,0,-1,-1,-1,-1,1,1,0,-1
1,-1,1,-1,1,-1,-1,1,0,-1
1,1,-1,1,1,-1,1,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
0,0,1,0,0,1,-1,-1,0,1
-1,-1,-1,-1,-1,-1,0,1,0,1
1,0,0,-1,-1,0,0,-1,0,1
-1,0,0,-1,-1,1,0,-1,0,1
1,1,1,-1,1,-1,-1,1,0,-1
-1,0,1,0,-1,0,0,-1,0,-1
1,1,1,1,0,-1,0,1,0,-1
1,0,1,1,1,0,-1,1,1,-1
-1,-1,1,-1,-1,1,-1,-1,0,1
0,-1,-1,0,1,1,0,-1,0,1
-1,-1,1,0,-1,0,1,1,0,1
-1,-1,1,-1,-1,-1,1,1,0,0
1,1,-1,-1,1,-1,1,1,0,0
1,0,1,0,-1,-1,-1,1,0,-1
-1,-1,-1,-1,-1,1,1,-1,1,0
1,1,1,-1,1,-1,0,1,1,-1
-1,-1,1,-1,-1,0,-1,-1,0,1
1,-1,0,-1,-1,0,-1,1,0,1
1,0,1,1,1,-1,1,1,0,-1
1,-1,0,1,0,0,-1,1,0,1
1,-1,0,1,1,0,0,1,0,-1
1,1,1,-1,1,-1,1,1,1,-1
-1,-1,1,1,-1,1,0,-1,0,1
-1,-1,0,0,-1,1,0,-1,0,1
0,0,0,0,1,0,0,-1,0,1
1,1,1,1,1,1,0,1,1,-1
1,0,1,1,1,-1,1,1,0,-1
-1,-1,0,-1,-1,0,0,1,0,1
0,0,0,0,1,0,1,-1,1,1
-1,-1,0,1,-1,1,-1,1,0,1
-1,-1,1,-1,1,0,-1,-1,0,0
-1,-1,0,1,1,0,-1,-1,0,1
1,1,0,0,-1,0,0,1,0,-1
1,-1,1,-1,-1,0,-1,-1,0,1
1,1,1,1,1,-1,1,1,0,-1
1,0,1,0,-1,1,0,-1,0,-1
-1,0,1,-1,-1,-1,-1,1,0,1
-1,-1,1,0,1,1,0,-1,0,1
1,1,0,-1,1,0,-1,-1,0,-1
-1,0,-1,-1,-1,1,-1,-1,0,1
1,-1,1,1,1,0,0,1,0,-1
1,0,1,1,0,0,-1,1,1,-1
-1,0,-1,-1,-1,0,-1,1,0,1
1,1,1,-1,1,-1,0,1,0,-1
0,-1,-1,0,1,0,1,-1,0,1
-1,-1,1,1,-1,1,-1,-1,0,1
1,1,1,0,-1,1,1,-1,0,-1
1,1,1,1,1,0,-1,1,0,-1
-1,-1,-1,-1,-1,0,0,1,0,1
-1,-1,1,1,-1,1,-1,-1,0,-1
-1,-1,1,-1,1,0,-1,-1,0,0
1,0,1,-1,-1,-1,0,1,1,-1
-1,0,-1,0,-1,1,0,-1,0,1
1,0,1,0,-1,-1,1,1,0,-1
1,1,1,-1,-1,-1,0,1,0,-1
1,0,1,1,1,-1,1,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
0,-1,-1,0,0,0,1,-1,1,1
-1,-1,0,1,1,-1,-1,1,0,1
1,0,1,-1,1,0,1,1,0,-1
1,1,1,1,1,0,1,1,0,-1
-1,-1,1,-1,-1,0,1,-1,0,0
0,0,1,0,1,-1,0,1,1,1
-1,-1,0,1,1,-1,1,1,0,1
1,1,1,1,1,1,0,-1,1,-1
1,0,-1,1,1,0,0,-1,0,-1
-1,0,-1,-1,-1,0,0,1,0,1
1,0,-1,1,1,-1,1,1,0,-1
-1,-1,-1,-1,-1,1,0,-1,0,1
1,-1,1,-1,-1,0,1,1,0,0
1,-1,0,1,0,-1,1,1,0,1
-1,0,-1,-1,-1,-1,-1,1,0,1
-1,-1,0,-1,-1,0,1,-1,1,1
-1,-1,1,-1,-1,1,1,1,0,0
1,0,1,1,0,0,1,1,0,-1
-1,-1,0,-1,-1,0,-1,1,0,1
-1,0,-1,-1,1,1,-1,-1,0,1
1,0,1,0,-1,1,-1,-1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,-1,1,-1,1,1,0,-1
-1,0,0,-1,-1,0,0,-1,0,1
-1,0,1,-1,0,-1,-1,1,0,1
-1,-1,-1,1,-1,0,0,-1,0,1
1,-1,1,0,-1,-1,1,1,0,-1
-1,-1,-1,-1,-1,1,-1,-1,0,1
1,0,1,1,1,0,1,-1,0,-1
-1,-1,1,-1,0,1,0,-1,0,1
1,0,0,1,-1,0,-1,1,0,1
1,1,1,-1,1,-1,1,1,0,-1
1,-1,0,-1,-1,-1,1,1,0,1
1,-1,0,0,-1,-1,0,1,0,-1
1,0,1,-1,-1,0,1,1,1,0
-1,-1,1,1,1,0,1,1,1,1
0,-1,1,0,0,1,-1,-1,0,1
1,1,0,1,0,0,-1,-1,0,-1
1,-1,1,1,1,0,-1,1,0,-1
-1,0,0,-1,-1,1,-1,-1,0,-1
1,1,1,0,-1,0,0,1,0,-1
-1,1,1,0,0,-1,0,1,0,-1
1,0,-1,-1,1,0,-1,-1,0,-1
-1,-1,-1,-1,-1,1,1,-1,0,1
1,0,1,0,-1,-1,-1,1,0,-1
1,-1,1,0,-1,0,1,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
-1,1,-1,0,0,1,-1,-1,0,-1
1,0,1,-1,-1,0,-1,-1,0,1
0,-1,0,-1,-1,1,-1,-1,0,1
1,0,1,-1,1,1,1,-1,0,-1
-1,-1,0,1,-1,0,0,1,0,1
1,0,1,-1,1,1,-1,-1,1,-1
-1,0,-1,0,-1,1,-1,-1,0,1
1,-1,1,1,1,-1,0,1,0,-1
-1,0,0,0,-1,-1,-1,1,0,0
-1,-1,1,-1,-1,0,-1,1,0,1
1,1,1,1,1,-1,-1,1,1,-1
0,-1,-1,0,1,1,-1,-1,0,1
1,-1,1,-1,-1,0,-1,-1,0,1
1,-1,0,-1,-1,-1,0,1,0,1
0,-1,-1,0,0,-1,0,1,0,1
-1,-1,0,1,1,-1,0,1,0,1
-1,0,0,1,1,1,-1,-1,0,1
-1,-1,0,-1,-1,1,0,-1,0,1
1,-1,0,-1,-1,1,-1,1,0,1
-1,0,1,-1,-1,-1,0,1,0,1
1,0,-1,0,-1,1,1,-1,0,-1
1,-1,1,-1,1,-1,0,1,0,-1
1,-1,0,1,1,0,-1,1,0,-1
1,-1,1,-1,1,-1,-1,1,1,-1
1,-1,1,1,1,0,-1,1,0,-1
1,0,1,-1,1,-1,-1,1,0,-1
1,-1,1,-1,1,-1,0,1,0,-1
1,-1,1,-1,0,1,-1,-1,0,0
-1,1,1,-1,1,1,1,-1,1,0
-1,-1,1,-1,1,1,0,-1,0,0
-1,0,-1,-1,1,1,0,-1,0,1
1,-1,0,1,0,-1,0,1,0,-1
-1,-1,1,-1,-1,-1,1,1,1,1
0,-1,-1,0,0,-1,0,1,0,1
1,0,1,0,-1,1,-1,-1,0,-1
1,-1,1,0,-1,-1,-1,1,0,-1
1,0,1,1,1,0,-1,1,0,-1
1,-1,0,1,1,-1,1,1,0,-1
0,0,-1,0,1,0,1,-1,1,1
1,1,1,-1,-1,-1,0,1,0,-1
0,-1,-1,0,0,1,0,-1,0,1
1,1,1,-1,-1,-1,-1,1,0,-1
1,0,1,-1,1,1,1,-1,1,-1
1,0,1,1,1,0,0,1,0,-1
1,-1,0,-1,-1,1,-1,-1,0,1
1,1,1,0,0,0,0,1,0,-1
-1,0,1,-1,1,1,1,-1,1,0
0,-1,1,0,0,0,1,1,1,1
1,0,0,1,1,0,-1,1,0,-1
1,-1,1,-1,1,1,-1,-1,0,-1
1,1,1,-1,1,0,0,-1,1,-1
1,-1,0,0,-1,1,-1,-1,0,1
1,1,1,-1,1,-1,1,1,0,-1
-1,-1,1,-1,0,1,1,-1,0,1
-1,-1,1,0,-1,1,-1,1,0,1
1,0,1,-1,1,0,0,1,0,-1
1,-1,0,0,-1,0,-1,-1,0,-1
1,0,1,-1,1,0,0,1,0,-1
1,1,-1,-1,1,1,-1,-1,0,-1
1,0,1,1,1,-1,0,1,1,-1
1,-1,0,-1,-1,0,-1,1,0,1
1,-1,1,-1,0,1,-1,1,0,0
1,0,1,1,1,1,1,-1,0,-1
1,-1,0,-1,-1,0,0,-1,0,1
1,-1,1,0,0,-1,0,1,0,-1
1,-1,0,1,0,1,0,1,0,1
1,0,1,-1,-1,0,1,1,0,0
-1,-1,1,0,-1,1,1,-1,1,1
1,0,1,0,0,0,0,1,0,-1
1,0,1,-1,-1,-1,0,1,0,-1
0,0,-1,0,1,-1,-1,1,0,1
1,1,1,1,1,-1,0,1,0,-1
0,0,1,0,1,-1,1,1,0,1
-1,0,0,-1,-1,0,0,-1,0,1
-1,0,1,0,0,0,0,1,1,-1
1,0,1,0,-1,-1,-1,1,0,-1
-1,0,-1,-1,-1,-1,1,1,0,1
0,-1,-1,0,1,-1,0,1,0,1
1,1,1,1,0,-1,0,1,0,-1
0,0,-1,0,1,0,-1,-1,0,1
-1,0,1,0,-1,-1,0,1,0,-1
1,1,1,0,-1,1,0,-1,0,-1
1,-1,1,-1,-1,0,0,1,0,-1
0,-1,1,-1,-1,1,-1,1,0,1
-1,-1,1,0,-1,0,1,1,1,1
1,1,1,-1,-1,-1,1,1,0,0
1,0,1,1,1,1,0,-1,0,-1
1,0,1,1,0,-1,1,1,0,-1
1,0,1,1,0,-1,0,1,0,-1
-1,-1,-1,-1,-1,1,0,-1,0,1
1,0,1,0,-1,1,0,-1,0,-1
1,-1,1,0,-1,1,-1,-1,1,-1
-1,-1,1,-1,-1,1,0,-1,0,-1
1,0,-1,0,0,1,0,-1,0,-1
-1,0,0,0,-1,-1,-1,1,0,0
1,-1,1,-1,1,-1,0,1,0,-1
-1,-1,0,-1,-1,-1,-1,1,0,0
0,0,1,0,1,0,1,-1,0,1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,0,-1,1,0,-1,0,-1
-1,-1,1,-1,-1,1,0,1,0,1
1,0,1,1,1,0,-1,1,0,-1
-1,-1,1,0,-1,1,-1,-1,0,1
1,0,1,1,1,1,0,-1,0,-1
1,0,1,1,1,0,0,1,0,-1
0,-1,-1,0,1,-1,-1,1,0,1
-1,-1,0,0,-1,1,-1,-1,0,1
1,-1,1,-1,-1,0,0,-1,0,1
1,0,-1,-1,-1,1,0,-1,0,1
1,-1,1,-1,1,-1,1,1,0,-1
1,0,0,1,1,1,1,-1,1,-1
1,-1,0,-1,1,1,0,-1,0,1
-1,0,0,-1,-1,-1,0,1,0,1
1,1,1,1,1,0,0,1,0,-1
-1,-1,1,-1,1,0,0,-1,0,0
1,0,1,0,-1,0,-1,1,0,-1
1,0,1,-1,1,1,-1,-1,0,-1
1,0,1,0,-1,1,1,-1,1,-1
-1,0,0,-1,-1,-1,-1,1,0,0
1,0,-1,-1,-1,1,0,-1,0,-1
1,0,0,-1,-1,0,0,-1,0,-1
1,1,1,-1,1,0,1,1,0,-1
1,1,1,1,1,1,0,-1,1,-1
0,0,-1,0,0,0,0,-1,0,1
1,0,1,1,0,0,1,1,0,-1
-1,0,0,0,-1,1,0,-1,0,1
1,-1,0,1,1,0,0,1,0,1
1,0,-1,1,0,1,-1,1,0,-1
-1,-1,-1,-1,-1,-1,-1,1,0,1
-1,-1,0,0,-1,1,-1,1,0,1
1,-1,0,1,1,-1,1,1,0,1
1,-1,0,-1,-1,-1,-1,1,1,1
-1,-1,0,-1,-1,0,-1,-1,0,1
-1,0,1,-1,-1,1,0,-1,0,1
-1,0,-1,-1,-1,0,0,-1,0,1
-1,0,-1,0,-1,-1,1,1,1,1
-1,-1,1,-1,0,1,0,-1,0,-1
1,1,1,-1,-1,1,0,-1,0,-1
1,0,-1,-1,1,-1,1,1,0,0
-1,-1,1,-1,-1,1,0,-1,0,1
1,-1,1,1,1,-1,0,1,0,-1
0,-1,-1,0,1,1,-1,-1,0,1
-1,-1,-1,0,-1,1,1,-1,0,1
1,0,1,0,-1,0,1,1,0,-1
1,0,1,0,-1,-1,1,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
1,0,1,1,1,0,0,1,0,-1
-1,-1,1,0,0,1,-1,-1,0,-1
1,0,1,-1,1,1,1,-1,0,-1
-1,-1,1,-1,-1,0,-1,1,0,1
1,1,1,0,-1,-1,-1,1,0,-1
-1,0,1,-1,-1,0,0,1,0,1
1,1,1,0,-1,-1,0,1,0,-1
1,1,1,1,1,-1,1,1,0,-1
1,1,1,0,-1,-1,-1,1,1,-1
-1,-1,1,0,-1,0,-1,1,0,1
1,1,1,1,1,-1,1,1,1,-1
1,-1,1,0,-1,1,0,-1,0,-1
-1,0,1,-1,0,0,-1,1,0,-1
1,0,1,0,-1,-1,0,1,0,-1
1,0,0,1,1,-1,-1,1,0,-1
1,0,1,1,1,0,1,-1,0,-1
-1,0,1,0,-1,-1,1,1,0,-1
-1,-1,-1,-1,-1,1,-1,-1,0,1
1,-1,1,1,1,0,1,1,0,-1
1,0,0,1,1,1,0,-1,0,-1
-1,0,0,0,-1,0,0,-1,0,1
1,-1,0,1,0,1,0,1,0,-1
1,1,1,1,1,0,1,-1,1,-1
-1,0,-1,-1,-1,1,-1,-1,0,1
1,-1,0,-1,-1,0,1,1,0,1
0,-1,-1,0,0,1,0,-1,0,1
1,0,1,-1,1,0,1,1,0,-1
1,-1,1,1,0,0,1,1,0,-1
0,0,0,0,0,0,0,1,0,0
-1,0,-1,0,-1,-1,1,1,0,1
1,-1,1,-1,1,-1,1,1,0,-1
-1,1,1,0,0,0,1,1,1,-1
1,0,1,1,1,-1,0,1,0,-1
-1,-1,0,-1,-1,0,-1,-1,0,1
1,1,0,-1,-1,1,0,-1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
-1,-1,-1,0,-1,1,-1,-1,0,1
1,-1,0,-1,0,1,-1,1,0,-1
0,0,-1,0,1,1,-1,-1,0,1
1,1,1,-1,-1,-1,1,1,0,-1
-1,-1,-1,0,-1,-1,-1,1,0,1
1,-1,0,1,1,-1,1,1,0,-1
-1,-1,-1,-1,-1,0,-1,1,0,1
1,0,1,0,-1,1,0,-1,0,-1
1,0,1,0,-1,-1,0,1,0,-1
1,-1,-1,1,1,-1,1,1,0,-1
1,-1,1,-1,1,1,1,-1,0,-1
-1,0,0,-1,-1,-1,0,1,0,0
1,0,1,-1,1,0,1,-1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,0,1,1,1,-1,-1,1,0,-1
0,-1,0,0,1,-1,0,1,0,1
-1,-1,1,-1,0,1,0,1,0,1
1,0,1,-1,-1,1,0,-1,0,-1
1,0,1,-1,-1,-1,0,1,0,-1
-1,0,0,-1,-1,1,0,-1,0,1
-1,0,-1,1,1,0,0,1,0,1
1,0,1,1,1,0,0,-1,0,-1
1,1,0,1,1,-1,0,1,0,-1
1,-1,1,0,-1,1,-1,1,0,1
1,0,1,-1,-1,1,0,-1,1,-1
1,1,0,0,-1,-1,1,1,1,-1
1,-1,1,1,1,1,-1,-1,0,-1
1,-1,1,1,1,0,0,1,0,-1
1,0,1,0,-1,-1,-1,1,1,-1
1,1,-1,-1,1,1,0,-1,0,-1
0,0,-1,0,1,1,0,-1,0,1
-1,0,-1,0,-1,-1,1,1,1,1
1,0,0,-1,-1,0,-1,-1,0,1
1,0,1,-1,-1,0,1,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
1,0,1,0,-1,1,-1,-1,1,-1
1,1,1,-1,-1,0,0,1,0,-1
1,0,1,1,1,0,1,1,0,-1
-1,-1,-1,-1,1,0,-1,1,0,1
-1,0,-1,1,-1,1,-1,-1,0,1
0,0,1,1,1,0,-1,1,0,-1
0,0,-1,0,1,1,0,-1,0,1
-1,0,-1,-1,-1,1,0,-1,0,1
1,-1,1,1,1,-1,-1,1,1,-1
1,-1,0,-1,1,0,0,1,0,-1
1,0,1,1,1,0,1,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
1,-1,0,1,1,1,-1,-1,0,-1
1,0,1,0,-1,1,1,-1,0,-1
1,0,-1,-1,1,-1,1,1,0,0
1,0,1,-1,1,-1,1,1,0,-1
-1,-1,-1,-1,-1,-1,1,1,0,1
-1,0,1,0,0,0,0,1,0,-1
0,-1,0,0,0,-1,0,1,0,0
1,1,1,1,1,-1,1,1,0,-1
1,0,0,0,-1,0,-1,-1,0,-1
0,-1,1,0,0,1,1,1,0,1
1,0,1,-1,1,0,-1,1,0,-1
0,-1,1,0,1,1,-1,-1,0,1
1,0,1,0,-1,0,1,-1,0,-1
1,-1,0,-1,-1,-1,0,1,0,-1
1,-1,0,1,1,-1,1,1,0,1
0,0,0,0,0,0,0,1,0,0
1,0,1,0,-1,0,1,1,0,-1
1,1,1,0,0,-1,0,1,0,-1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,-1,1,0,0,-1,0,-1
-1,-1,1,-1,1,0,-1,1,0,0
0,-1,0,0,0,0,0,1,0,0
-1,-1,-1,-1,-1,1,-1,1,0,1
-1,-1,-1,-1,-1,0,-1,-1,0,1
1,0,1,-1,-1,-1,1,1,0,0
1,0,0,-1,1,1,0,-1,0,-1
-1,0,-1,-1,1,1,-1,-1,0,1
1,0,1,0,0,-1,0,1,0,-1
0,0,0,0,0,-1,-1,1,0,0
1,0,1,-1,1,-1,-1,1,1,-1
1,0,1,1,1,0,0,1,0,-1
1,-1,0,1,1,0,0,1,0,-1
1,-1,0,-1,-1,0,1,1,1,1
-1,-1,-1,-1,-1,1,0,1,0,1
1,1,1,1,0,1,0,-1,0,-1
-1,-1,0,1,1,0,0,1,0,-1
-1,0,-1,-1,-1,0,0,-1,0,1
1,0,1,1,1,1,-1,1,0,-1
-1,0,1,-1,-1,0,0,-1,0,1
-1,-1,0,-1,-1,-1,0,1,0,0
-1,0,1,0,-1,0,0,1,0,-1
1,1,0,1,1,-1,-1,1,0,-1
0,-1,-1,0,1,1,1,-1,1,1
1,1,1,1,1,-1,0,1,1,-1
1,-1,0,-1,1,0,0,1,0,-1
1,0,1,0,-1,0,1,1,0,-1
1,-1,1,-1,1,-1,-1,1,0,-1
-1,-1,1,-1,-1,0,-1,-1,0,1
1,-1,0,0,-1,1,1,-1,0,1
-1,0,1,0,-1,0,0,1,0,1
1,0,1,-1,1,1,1,-1,1,-1
0,-1,1,0,1,-1,1,1,1,1
1,0,1,0,0,0,0,-1,0,-1
1,0,1,-1,-1,1,1,-1,0,0
1,-1,1,1,1,-1,1,1,0,-1
-1,-1,0,-1,-1,0,-1,-1,0,1
0,-1,1,0,1,-1,1,1,0,1
1,-1,0,1,1,0,-1,-1,0,-1
1,0,1,0,0,0,0,1,1,-1
-1,0,0,-1,-1,1,0,-1,0,1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,1,0,0,0,1,0,-1
1,1,1,0,0,0,0,-1,0,-1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,0,-1,0,1,-1,0,-1
1,0,1,1,1,0,0,1,0,-1
1,-1,1,0,-1,-1,0,1,0,-1
1,-1,0,-1,-1,-1,0,1,0,1
1,0,1,1,1,-1,1,1,0,-1
1,1,1,-1,1,1,-1,-1,0,-1
1,0,1,1,1,-1,0,1,0,-1
1,1,1,0,-1,0,1,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
-1,1,1,-1,1,-1,0,1,0,0
1,0,1,-1,1,-1,0,1,0,-1
-1,0,1,0,0,0,0,1,0,-1
1,0,0,1,1,0,-1,1,0,-1
1,-1,1,1,1,0,0,1,0,-1
1,0,-1,-1,1,-1,0,1,0,-1
1,1,1,-1,1,0,0,1,0,-1
1,1,1,-1,1,1,0,-1,0,-1
1,1,0,0,-1,0,-1,1,0,-1
-1,-1,1,-1,-1,1,-1,1,0,-1
1,-1,1,1,1,1,1,-1,0,-1
1,0,1,1,1,0,0,1,0,-1
-1,0,0,-1,-1,1,-1,-1,0,1
-1,0,-1,-1,-1,1,-1,1,0,1
-1,-1,0,-1,-1,1,-1,1,0,1
1,0,1,0,0,-1,0,1,0,-1
0,-1,0,-1,-1,0,-1,-1,0,1
-1,-1,1,1,1,1,-1,-1,0,1
1,0,1,-1,-1,1,1,-1,0,-1
0,-1,-1,0,0,1,1,-1,1,1
-1,-1,0,-1,-1,0,1,1,1,1
1,0,1,1,1,0,-1,-1,0,-1
-1,0,1,1,1,-1,0,1,1,-1
1,1,1,1,1,1,0,1,0,-1
0,-1,1,0,1,-1,0,1,0,1
1,0,1,1,1,0,0,1,0,-1
-1,0,-1,-1,-1,1,-1,-1,0,1
1,0,-1,1,1,0,1,-1,0,-1
1,0,1,1,0,-1,-1,1,0,-1
1,0,-1,1,1,1,0,-1,0,-1
-1,-1,0,-1,0,0,0,1,0,1
1,0,-1,1,-1,-1,1,1,0,-1
-1,0,-1,-1,-1,0,0,1,0,1
1,0,1,0,0,0,-1,1,0,-1
1,0,1,1,1,1,1,-1,0,-1
1,0,1,0,0,1,-1,-1,0,-1
1,0,1,1,0,1,0,-1,0,-1
-1,1,1,-1,1,-1,0,1,1,0
1,0,1,1,1,0,0,1,0,-1
1,-1,0,0,-1,1,-1,-1,0,-1
1,-1,0,-1,1,-1,1,1,0,-1
-1,0,0,-1,-1,-1,0,1,0,0
-1,-1,-1,-1,-1,-1,0,1,0,1
1,1,1,-1,1,1,1,-1,1,-1
0,0,-1,0,0,1,0,-1,0,1
1,1,1,-1,1,-1,0,1,0,-1
1,0,1,-1,1,-1,-1,1,1,-1
1,0,1,0,-1,0,-1,1,0,-1
1,0,1,1,0,-1,0,1,0,-1
0,-1,-1,0,1,1,-1,1,0,1
-1,-1,1,0,0,1,-1,-1,0,1
1,0,-1,-1,1,1,0,1,0,-1
0,0,0,0,1,0,-1,1,0,1
1,-1,1,-1,1,-1,0,1,0,-1
-1,-1,1,1,1,1,-1,-1,0,1
1,1,1,-1,-1,-1,0,1,1,-1
1,-1,0,-1,1,0,0,1,0,1
-1,0,-1,-1,-1,0,-1,1,0,1
1,0,1,0,0,1,1,-1,0,-1
0,0,1,0,1,0,-1,-1,0,1
-1,-1,1,-1,-1,-1,-1,1,1,1
1,-1,0,1,-1,1,-1,-1,0,1
-1,-1,1,-1,-1,1,1,1,0,0
0,-1,-1,0,0,1,0,-1,0,1
1,0,1,1,1,0,1,1,0,-1
1,1,-1,1,0,1,0,-1,0,-1
1,1,1,0,-1,0,1,1,0,-1
-1,-1,-1,-1,-1,-1,0,1,0,1
-1,0,1,-1,-1,-1,1,1,0,0
0,0,-1,0,1,-1,0,1,0,1
0,0,0,0,0,-1,1,1,1,0
0,-1,1,0,1,-1,0,1,0,1
0,-1,1,-1,-1,0,-1,-1,0,1
1,0,1,1,1,0,0,-1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,1,-1,-1,-1,1,0,-1,0,-1
1,0,-1,-1,-1,1,0,1,0,-1
-1,0,-1,-1,-1,1,-1,-1,0,1
-1,0,-1,0,-1,0,0,1,0,1
1,0,1,-1,1,-1,1,1,0,-1
1,0,1,0,-1,-1,0,1,0,-1
-1,-1,0,0,-1,0,0,1,0,1
-1,0,0,-1,-1,0,-1,1,0,1
1,-1,0,1,1,1,-1,1,0,-1
1,0,1,1,1,0,1,1,0,-1
-1,0,-1,-1,-1,0,-1,1,0,1
-1,0,1,-1,-1,-1,0,1,1,1
-1,0,0,-1,-1,0,-1,1,0,1
0,0,-1,0,1,1,1,-1,0,1
-1,-1,0,-1,-1,1,1,-1,1,1
1,-1,0,1,1,-1,0,1,1,1
1,0,1,1,1,-1,1,1,0,-1
-1,0,-1,-1,-1,-1,-1,1,0,1
0,0,0,0,1,1,-1,-1,0,1
1,-1,0,-1,-1,0,0,1,0,1
-1,0,1,0,-1,1,1,-1,0,-1
-1,0,-1,0,-1,-1,0,1,0,1
1,0,1,0,-1,0,0,-1,0,-1
1,-1,0,0,-1,0,0,1,0,1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,1,1,-1,1,0,1,1,0,-1
1,0,1,0,0,-1,1,1,1,-1
-1,0,1,-1,1,-1,1,1,0,1
-1,0,-1,0,-1,1,-1,-1,0,1
-1,0,1,0,0,0,0,-1,0,-1
0,0,-1,0,0,1,-1,1,0,1
1,0,1,0,0,1,1,-1,1,-1
1,0,1,-1,1,0,0,-1,0,-1
-1,-1,0,-1,-1,1,-1,-1,0,1
1,-1,1,0,0,1,0,-1,0,-1
1,1,1,-1,1,-1,0,1,1,-1
1,1,1,0,-1,-1,-1,1,0,-1
1,-1,0,-1,0,-1,-1,1,0,-1
0,0,1,0,1,0,-1,1,0,1
1,0,1,-1,1,1,0,1,0,-1
1,1,1,1,1,-1,0,1,1,-1
1,0,1,0,-1,0,1,1,0,-1
1,1,1,-1,-1,-1,0,1,0,-1
1,0,1,-1,-1,1,1,1,0,-1
1,-1,0,1,-1,-1,-1,1,0,-1
1,0,1,0,-1,0,1,-1,0,-1
1,0,1,0,-1,-1,0,1,0,-1
-1,-1,0,-1,-1,0,0,-1,0,1
1,0,1,1,1,1,0,-1,0,-1
-1,-1,-1,0,-1,-1,1,1,0,1
0,-1,-1,0,1,0,-1,-1,0,1
1,-1,1,-1,1,1,-1,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
1,-1,1,-1,1,1,0,1,0,-1
-1,-1,-1,-1,-1,0,0,-1,0,1
0,-1,0,0,1,-1,-1,1,0,1
-1,-1,1,1,-1,1,0,-1,0,-1
-1,-1,1,1,1,1,0,-1,0,1
1,1,1,1,1,0,-1,1,0,-1
1,-1,1,-1,1,-1,0,1,0,-1
1,-1,1,-1,1,0,0,1,0,-1
1,-1,0,0,-1,0,0,1,0,1
1,0,-1,-1,1,-1,0,1,0,-1
1,1,1,-1,1,-1,-1,1,0,-1
1,0,1,1,0,0,1,1,0,-1
1,-1,0,-1,1,0,-1,1,0,1
0,0,-1,0,0,0,-1,-1,0,1
1,0,-1,-1,-1,1,-1,-1,0,1
-1,0,0,-1,-1,0,0,-1,0,1
0,-1,-1,0,1,0,-1,-1,0,1
1,0,1,1,1,0,0,1,0,-1
-1,0,1,0,0,-1,1,1,0,-1
-1,0,-1,1,1,1,0,1,0,-1
1,1,1,1,1,-1,0,1,0,-1
-1,-1,1,-1,-1,0,-1,1,1,1
0,-1,0,0,1,1,0,1,0,1
-1,0,0,-1,-1,0,0,1,0,1
1,0,0,-1,1,-1,-1,1,1,-1
1,0,1,0,-1,0,0,-1,0,-1
1,-1,0,-1,-1,0,-1,1,0,-1
1,0,1,1,1,1,-1,1,0,-1
1,0,1,0,-1,-1,-1,1,0,-1
-1,-1,0,-1,-1,1,0,1,0,1
1,0,1,1,0,-1,0,1,0,-1
0,-1,0,0,1,0,0,1,0,1
1,1,1,-1,1,-1,-1,1,0,-1
-1,-1,0,0,-1,-1,-1,1,0,0
-1,-1,1,-1,-1,1,1,1,0,0
1,1,1,-1,1,-1,0,1,0,-1
1,1,1,1,1,1,1,-1,0,-1
1,-1,1,-1,1,1,1,-1,0,-1
1,0,1,1,1,-1,1,1,0,-1
0,0,-1,0,1,1,0,-1,0,1
1,-1,1,-1,-1,1,-1,-1,0,1
0,-1,-1,0,1,-1,0,1,0,1
1,0,1,-1,1,0,1,1,0,-1
-1,-1,-1,0,-1,1,-1,-1,0,1
1,-1,0,-1,1,1,0,1,0,-1
1,-1,1,1,1,-1,1,1,0,-1
1,0,0,1,1,-1,1,1,0,-1
1,1,1,-1,1,-1,0,1,1,-1
-1,-1,1,-1,1,1,1,1,0,0
1,0,1,0,-1,0,0,1,0,-1
1,0,1,1,1,0,-1,1,0,-1
1,0,1,-1,-1,-1,-1,1,1,-1
-1,-1,1,0,-1,1,-1,1,0,-1
1,0,1,-1,-1,1,-1,1,0,-1
1,1,1,-1,-1,-1,-1,1,0,-1
1,1,1,1,1,-1,1,1,0,-1
1,0,1,0,-1,-1,-1,1,0,-1
1,0,1,-1,-1,-1,1,1,0,0
-1,-1,-1,-1,-1,1,0,-1,0,1
-1,0,-1,-1,-1,1,0,-1,0,1
1,-1,1,-1,-1,1,1,-1,0,0
1,-1,1,1,1,-1,-1,1,0,-1
1,1,1,-1,1,-1,0,1,0,-1
1,-1,0,0,-1,0,1,-1,0,1
1,-1,0,-1,-1,0,-1,1,0,1
-1,-1,0,-1,-1,1,-1,-1,0,1
1,0,1,1,1,1,1,1,0,-1
1,0,1,-1,1,1,1,1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,1,1,-1,1,-1,1,1,0,-1
1,0,1,1,1,-1,-1,1,0,-1
-1,-1,-1,-1,-1,0,-1,-1,0,1
1,0,1,-1,-1,-1,-1,1,0,-1
1,-1,1,-1,-1,0,0,1,0,-1
-1,-1,0,0,-1,0,-1,-1,0,1
1,-1,0,-1,1,0,-1,1,0,-1
1,1,0,1,1,-1,-1,1,0,-1
1,0,1,1,0,0,-1,-1,1,-1
1,0,-1,1,1,-1,1,1,0,-1
1,0,1,0,-1,0,-1,-1,0,-1
1,0,1,1,1,0,1,-1,0,-1
-1,-1,1,0,0,-1,0,1,0,-1
0,-1,0,0,0,1,-1,-1,0,0
-1,-1,-1,0,-1,-1,0,1,0,1
1,-1,0,-1,-1,-1,1,1,0,-1
1,1,-1,1,1,1,0,-1,0,-1
1,1,1,1,0,0,0,1,0,-1
-1,-1,0,1,1,-1,0,1,0,-1
-1,0,-1,-1,-1,1,0,1,0,1
1,0,1,-1,1,0,0,1,0,-1
1,0,1,-1,1,-1,1,1,1,-1
1,0,1,1,0,0,0,1,0,-1
1,0,1,-1,1,-1,-1,1,0,-1
1,0,1,-1,-1,0,0,1,0,-1
1,0,1,1,1,1,0,-1,0,-1
1,1,1,1,1,-1,-1,1,0,-1
-1,-1,1,-1,-1,0,0,1,0,1
1,-1,0,-1,1,-1,-1,1,0,-1
1,0,1,0,-1,0,1,1,0,-1
1,1,1,-1,-1,1,1,-1,1,0
1,0,1,1,1,1,1,1,1,-1
-1,-1,-1,-1,-1,0,0,1,0,1
1,0,1,-1,1,0,-1,1,0,-1
-1,-1,-1,-1,-1,1,-1,-1,0,1
1,0,1,-1,1,1,1,1,1,-1
-1,0,1,-1,-1,0,-1,-1,0,1
-1,-1,1,0,-1,0,0,1,0,1
-1,-1,0,-1,-1,-1,-1,1,0,1
1,-1,0,1,1,-1,-1,1,0,-1
1,1,1,1,1,-1,1,1,0,-1
1,1,1,1,0,-1,-1,1,1,-1
-1,0,0,-1,1,0,-1,1,0,1
-1,-1,1,-1,-1,-1,0,1,0,1
0,-1,0,0,1,-1,0,1,0,1
-1,-1,0,-1,-1,0,-1,1,0,1
1,-1,0,-1,0,0,-1,-1,0,1
1,0,0,1,-1,0,0,1,0,-1
-1,-1,0,0,-1,1,0,-1,0,1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,1,1,1,0,1,0,-1
1,1,1,0,0,-1,0,1,1,-1
-1,1,1,0,0,-1,1,1,0,-1
0,-1,-1,0,0,0,1,-1,1,1
1,0,-1,1,-1,0,0,-1,0,-1
0,-1,0,0,1,1,0,-1,0,1
1,0,1,-1,1,0,0,1,0,-1
1,0,1,-1,1,-1,-1,1,0,-1
1,0,1,0,-1,1,0,-1,0,-1
-1,-1,0,-1,-1,1,0,-1,0,1
-1,-1,-1,0,-1,1,1,-1,1,1
1,-1,1,-1,-1,1,-1,-1,0,1
1,0,1,0,-1,1,0,-1,1,-1
-1,0,0,0,-1,1,-1,1,0,1
1,-1,1,-1,1,0,1,1,0,-1
1,-1,1,-1,-1,1,-1,-1,0,1
-1,-1,1,-1,1,1,0,1,0,0
-1,-1,-1,-1,-1,0,0,-1,0,1
1,0,1,1,1,0,1,-1,0,-1
0,-1,-1,0,1,0,-1,1,0,1
-1,0,1,1,1,0,1,1,0,-1
0,0,-1,0,1,1,0,-1,0,1
0,0,1,0,1,-1,0,1,1,1
0,-1,0,0,1,-1,0,1,0,1
0,-1,0,0,0,0,0,1,0,0
-1,-1,1,-1,0,1,-1,-1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,-1,-1,-1,1,1,0,0
1,0,1,1,0,1,0,-1,0,-1
-1,0,1,-1,-1,1,-1,-1,0,1
1,0,1,0,-1,-1,0,1,0,-1
-1,-1,0,-1,-1,1,0,-1,0,1
-1,-1,-1,-1,1,0,0,1,0,1
-1,-1,1,-1,-1,-1,0,1,0,1
0,0,-1,0,1,0,0,-1,0,1
-1,-1,-1,0,-1,-1,0,1,0,1
1,1,1,1,1,1,-1,-1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,-1,-1,1,0,-1,0,-1
1,0,1,1,0,1,0,-1,1,-1
1,0,1,-1,1,1,0,-1,0,-1
-1,-1,0,-1,-1,-1,1,1,1,0
-1,-1,-1,-1,-1,0,0,-1,0,1
1,1,1,-1,1,0,0,-1,0,-1
1,0,1,-1,-1,-1,-1,1,0,-1
1,1,1,-1,1,0,-1,-1,0,-1
1,1,-1,1,1,-1,1,1,0,-1
-1,-1,0,-1,-1,-1,-1,1,0,1
1,1,1,0,-1,-1,0,1,0,-1
1,1,1,-1,1,0,-1,1,0,-1
0,-1,-1,0,1,1,0,-1,0,1
-1,-1,1,-1,-1,-1,-1,1,0,1
1,-1,1,1,1,0,-1,1,0,-1
1,-1,1,1,0,-1,0,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
1,0,1,-1,1,1,0,-1,0,-1
1,1,1,-1,-1,-1,0,1,0,-1
0,-1,0,0,1,0,0,-1,0,1
1,0,1,1,1,1,-1,-1,0,-1
1,0,1,-1,-1,-1,0,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
0,-1,-1,0,0,1,1,-1,0,1
1,0,1,-1,1,1,-1,-1,1,-1
0,-1,-1,0,1,1,1,-1,0,1
-1,-1,0,-1,-1,1,-1,-1,0,1
-1,-1,1,-1,-1,1,0,1,0,1
1,-1,0,-1,-1,-1,0,1,0,-1
-1,-1,1,-1,-1,0,-1,-1,0,1
-1,-1,-1,-1,-1,0,0,1,0,1
-1,0,0,-1,-1,0,0,-1,0,1
-1,0,0,-1,-1,0,1,-1,1,1
1,1,1,-1,1,0,1,1,0,-1
1,0,1,-1,1,1,0,-1,0,-1
1,-1,0,1,1,-1,-1,1,0,1
0,-1,-1,0,1,1,0,-1,0,1
1,-1,1,1,1,0,-1,1,0,-1
-1,-1,0,-1,-1,-1,0,1,0,0
-1,1,0,0,-1,-1,1,1,1,-1
0,0,-1,0,1,1,0,1,0,1
1,-1,0,-1,-1,-1,-1,1,0,1
1,-1,1,-1,-1,1,1,-1,0,0
1,0,-1,1,1,0,0,-1,0,-1
1,0,0,1,-1,0,0,1,0,-1
-1,-1,1,0,-1,0,1,-1,1,1
-1,-1,0,-1,-1,0,0,1,0,1
0,-1,0,0,0,-1,0,1,0,0
1,0,1,0,-1,0,1,1,0,-1
-1,-1,0,-1,-1,0,0,1,0,1
-1,0,-1,1,-1,1,-1,-1,0,1
1,0,-1,0,-1,0,1,-1,1,-1
1,-1,1,1,0,1,1,-1,0,-1
1,-1,-1,1,1,1,0,-1,0,1
1,1,1,-1,-1,-1,0,1,0,-1
-1,-1,1,-1,0,1,0,-1,0,1
1,0,1,-1,-1,1,1,-1,1,0
1,1,1,1,1,0,1,1,0,-1
1,-1,0,-1,-1,1,-1,-1,0,1
-1,-1,0,-1,-1,1,0,-1,0,1
0,-1,1,0,1,1,-1,-1,0,1
1,-1,1,0,1,0,-1,-1,0,-1
1,0,1,1,1,0,0,-1,0,-1
-1,-1,-1,-1,-1,1,0,-1,0,1
-1,0,0,-1,-1,1,-1,-1,0,1
1,0,-1,1,1,1,-1,-1,0,-1
0,0,1,0,1,1,0,-1,0,1
1,0,1,1,1,-1,0,1,0,-1
-1,-1,1,-1,-1,-1,-1,1,0,1
1,0,1,0,-1,1,-1,-1,0,-1
1,0,-1,1,1,1,-1,-1,0,-1
-1,-1,-1,-1,-1,0,-1,1,0,1
1,1,1,-1,1,1,1,-1,0,-1
1,-1,0,-1,1,-1,0,1,0,-1
-1,-1,1,-1,1,1,0,1,0,1
0,0,-1,0,0,0,1,-1,1,1
1,-1,0,1,1,-1,0,1,0,-1
1,0,1,-1,-1,0,0,1,0,-1
1,-1,0,-1,-1,0,0,1,0,1
1,0,1,0,-1,-1,0,1,0,-1
-1,0,-1,-1,-1,-1,-1,1,0,1
-1,-1,0,-1,-1,0,0,1,0,1
-1,1,1,-1,1,-1,0,1,0,0
1,0,1,0,-1,0,-1,1,0,-1
1,-1,1,-1,1,-1,1,1,0,-1
1,-1,1,1,1,1,1,-1,0,1
1,0,1,-1,0,0,-1,1,0,0
1,0,-1,1,1,1,1,-1,1,-1
1,0,0,1,1,0,0,1,0,1
-1,-1,0,0,-1,0,1,-1,1,1
1,0,1,-1,-1,0,-1,1,0,-1
1,0,1,0,0,-1,0,1,1,-1
1,0,1,1,1,-1,-1,1,0,-1
-1,0,-1,-1,1,0,-1,-1,0,1
1,0,-1,-1,1,1,0,1,0,-1
1,-1,1,-1,-1,0,-1,-1,0,1
1,0,1,-1,-1,-1,-1,1,0,-1
1,-1,0,0,-1,1,-1,-1,0,1
1,0,1,1,1,1,-1,-1,0,-1
1,1,1,1,1,0,1,1,0,-1
1,0,1,0,-1,-1,-1,1,0,-1
0,-1,0,0,0,-1,0,1,0,0
1,-1,1,1,0,-1,-1,1,0,-1
-1,-1,0,-1,-1,0,0,1,0,1
1,-1,1,0,-1,0,1,1,0,-1
1,1,1,-1,1,-1,-1,1,0,-1
1,0,1,0,-1,0,-1,1,0,-1
-1,-1,0,1,1,0,-1,1,0,1
-1,0,-1,1,1,0,0,-1,0,1
1,1,1,0,-1,1,-1,-1,0,-1
-1,0,-1,-1,-1,0,0,-1,0,1
1,-1,-1,1,1,1,-1,-1,0,1
-1,-1,1,-1,-1,1,-1,1,0,1
1,0,1,-1,-1,0,1,-1,0,0
1,0,1,1,0,0,0,-1,0,-1
0,0,-1,0,1,1,0,-1,0,1
1,0,1,-1,-1,1,-1,-1,0,1
-1,-1,1,0,-1,0,0,-1,0,1
1,-1,1,0,-1,-1,1,1,0,-1
0,-1,-1,0,1,1,-1,-1,0,1
1,1,1,-1,-1,1,1,-1,0,0
1,0,1,-1,1,-1,1,1,0,-1
1,0,1,1,1,0,-1,1,0,-1
1,1,1,1,1,1,0,-1,0,-1
0,-1,0,0,1,-1,0,1,0,1
1,1,1,-1,-1,-1,1,1,0,0
1,-1,0,1,1,0,-1,1,0,1
1,0,1,1,1,0,0,-1,0,-1
-1,0,-1,-1,-1,0,-1,-1,0,1
-1,-1,-1,0,-1,1,-1,-1,0,1
-1,-1,-1,0,-1,0,0,1,0,1
-1,-1,1,0,-1,1,-1,-1,0,-1
-1,-1,-1,-1,-1,0,0,-1,0,1
1,-1,0,-1,-1,-1,-1,1,0,-1
1,0,1,1,1,-1,1,1,0,-1
-1,-1,1,-1,-1,1,-1,-1,0,1
-1,-1,-1,-1,-1,1,0,-1,0,1
1,0,0,1,1,0,0,1,0,-1
1,0,-1,-1,1,-1,1,1,0,0
1,0,-1,1,1,-1,0,1,0,-1
-1,-1,0,-1,-1,1,-1,-1,0,1
1,0,-1,1,1,1,-1,-1,0,1
-1,-1,-1,-1,-1,-1,0,1,0,1
-1,0,-1,0,-1,0,1,-1,0,-1
0,-1,-1,0,0,0,0,1,0,1
0,0,0,0,0,-1,0,1,0,0
-1,0,1,1,1,-1,0,1,1,-1
1,1,1,-1,1,1,1,-1,0,-1
1,-1,0,-1,-1,0,-1,-1,0,1
-1,-1,0,-1,-1,-1,1,1,0,0
-1,0,0,0,-1,0,0,1,0,1
0,0,0,0,0,1,0,-1,0,0
-1,0,0,-1,-1,1,-1,-1,0,1
1,0,0,1,1,-1,0,1,0,-1
1,0,1,-1,-1,0,1,1,0,0
1,0,1,1,1,1,1,1,0,-1
1,1,1,-1,1,1,1,-1,0,-1
-1,-1,-1,0,-1,1,1,-1,1,1
1,0,1,1,1,-1,0,1,0,-1
-1,-1,0,0,-1,1,-1,-1,0,1
-1,0,-1,-1,-1,1,-1,-1,0,1
1,0,1,-1,1,-1,0,1,1,-1
-1,-1,1,1,-1,1,-1,-1,0,1
-1,-1,0,-1,1,-1,1,1,0,-1
-1,0,-1,-1,-1,0,-1,1,0,1
0,0,0,0,1,1,0,1,0,1
1,0,1,1,1,0,-1,-1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,1,1,-1,1,0,1,1,0,-1
1,1,1,1,0,0,1,1,0,-1
1,0,0,1,1,0,0,1,0,-1
-1,-1,1,-1,-1,-1,1,1,0,0
1,0,1,0,-1,0,1,1,0,-1
1,1,1,-1,1,-1,0,1,0,-1
1,0,1,0,0,0,1,1,1,-1
0,-1,-1,0,1,0,0,1,0,1
1,1,1,-1,1,0,1,-1,0,-1
1,-1,1,-1,-1,-1,1,1,0,0
1,0,0,1,1,1,-1,-1,0,-1
1,1,1,0,0,0,0,1,0,-1
1,0,1,-1,1,-1,1,1,0,-1
-1,-1,-1,0,-1,0,1,-1,0,1
1,0,1,-1,-1,-1,1,1,1,0
1,1,-1,-1,1,1,1,1,0,0
1,0,1,-1,1,0,-1,1,0,-1
-1,0,1,-1,0,-1,1,1,0,-1
-1,-1,0,-1,-1,0,-1,1,0,1
1,0,1,-1,1,0,1,-1,0,-1
-1,-1,1,-1,-1,0,0,1,0,1
-1,-1,-1,-1,1,1,0,-1,0,1
1,0,1,-1,-1,-1,0,1,0,-1
-1,-1,-1,-1,-1,-1,-1,1,0,1
1,1,1,-1,1,-1,0,1,0,-1
-1,-1,0,0,-1,1,-1,1,0,1
0,-1,1,0,1,0,-1,-1,0,1
-1,0,1,-1,1,1,1,-1,1,1
1,0,1,1,1,0,1,1,0,-1
1,1,1,-1,1,-1,1,1,0,-1
1,1,-1,1,1,0,1,1,0,-1
1,0,1,-1,1,0,1,1,0,-1
1,0,1,1,1,1,1,-1,0,-1
-1,-1,1,-1,-1,0,0,1,0,1
1,0,1,1,1,1,0,1,0,-1
0,0,-1,0,1,1,-1,-1,0,1
1,0,1,1,1,0,-1,-1,1,-1
1,0,-1,1,1,1,0,-1,0,-1
1,0,1,0,-1,0,1,-1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,0,1,-1,-1,-1,1,1,0,0
1,0,0,1,1,-1,0,1,0,-1
-1,-1,1,-1,-1,-1,1,1,0,0
1,0,-1,-1,-1,1,-1,-1,0,1
1,-1,1,1,1,0,1,1,0,-1
-1,0,-1,-1,-1,0,0,-1,0,1
1,0,1,-1,1,-1,0,1,0,-1
0,-1,0,0,1,1,0,-1,0,1
0,0,-1,0,1,-1,-1,1,0,1
1,0,-1,1,1,1,0,-1,0,-1
1,0,1,1,1,1,1,-1,0,-1
1,0,1,-1,-1,0,0,1,1,-1
1,0,-1,1,1,1,0,-1,0,1
-1,-1,-1,-1,1,1,-1,-1,0,1
1,0,0,0,-1,1,0,-1,0,-1
1,0,1,0,0,1,1,-1,1,-1
-1,-1,-1,-1,-1,0,-1,1,0,1
0,0,-1,0,1,0,0,1,0,1
1,1,1,-1,-1,-1,1,1,1,0
-1,-1,0,0,-1,1,-1,-1,0,1
1,0,1,1,1,1,-1,-1,0,-1
1,0,0,0,-1,-1,0,1,0,-1
1,0,1,1,1,1,0,-1,0,-1
-1,1,0,0,-1,0,-1,1,0,-1
1,0,-1,-1,1,1,-1,-1,0,-1
1,0,1,-1,1,-1,-1,1,1,-1
1,1,1,-1,1,-1,-1,1,1,-1
-1,0,-1,-1,-1,-1,0,1,0,1
-1,-1,-1,-1,1,1,-1,-1,0,1
-1,0,1,-1,0,1,-1,1,0,1
1,1,1,1,1,0,0,1,0,-1
-1,1,1,0,-1,0,1,-1,0,-1
1,0,1,-1,1,0,-1,-1,0,-1
1,0,1,-1,1,0,0,1,1,-1
-1,0,1,-1,0,1,0,1,0,1
1,0,1,-1,1,-1,1,1,1,-1
-1,0,1,0,-1,-1,0,1,0,-1
-1,0,-1,0,-1,1,0,-1,0,1
0,-1,-1,0,1,-1,-1,1,0,1
1,1,1,0,-1,-1,1,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
-1,-1,1,1,1,1,-1,-1,0,1
1,-1,1,1,1,-1,1,1,0,-1
1,0,-1,-1,-1,0,0,-1,0,-1
1,-1,0,-1,-1,1,-1,-1,0,1
1,1,1,-1,-1,-1,1,1,1,0
-1,1,1,0,0,-1,0,1,0,-1
-1,-1,0,-1,-1,1,-1,-1,0,1
0,0,0,0,0,-1,0,1,0,0
1,1,1,-1,1,0,1,1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,-1,-1,-1,0,1,1,-1
-1,-1,1,1,1,1,1,-1,0,1
-1,-1,0,1,1,1,-1,-1,0,1
-1,0,-1,-1,1,-1,-1,1,0,1
-1,0,-1,0,-1,0,1,1,1,1
-1,-1,-1,-1,-1,1,-1,-1,0,1
1,0,1,-1,-1,1,1,-1,0,0
1,0,1,0,-1,-1,1,1,0,-1
-1,-1,0,0,1,1,0,-1,0,1
-1,1,-1,1,1,-1,0,1,0,-1
1,1,0,0,-1,-1,-1,1,0,-1
-1,-1,1,-1,-1,1,0,1,0,1
0,0,-1,0,1,1,0,-1,0,1
1,0,1,0,-1,-1,-1,1,1,-1
0,-1,0,0,1,1,0,-1,0,1
0,0,-1,0,1,0,0,1,0,1
1,1,1,1,1,1,-1,-1,0,-1
-1,-1,0,-1,0,1,-1,-1,0,1
-1,0,-1,1,1,1,-1,-1,0,1
1,0,1,-1,1,-1,0,1,1,-1
1,0,0,1,-1,0,-1,1,0,1
-1,0,1,0,0,1,0,1,0,-1
-1,-1,0,0,-1,1,0,-1,0,1
-1,-1,1,-1,1,1,1,-1,0,0
0,-1,1,0,1,0,0,1,0,1
1,-1,1,-1,1,-1,0,1,0,-1
-1,-1,-1,-1,-1,0,0,-1,0,1
1,0,1,-1,1,1,0,1,0,-1
1,-1,0,1,1,-1,1,1,0,-1
-1,-1,0,-1,0,1,-1,-1,0,1
-1,0,1,-1,1,0,1,-1,1,1
-1,-1,0,-1,-1,0,0,1,0,1
1,0,-1,-1,1,0,1,1,0,-1
-1,-1,-1,1,1,1,-1,-1,0,1
-1,0,-1,1,1,1,1,-1,0,1
1,-1,0,-1,-1,1,-1,-1,0,1
-1,0,0,-1,-1,0,-1,1,0,1
1,-1,0,-1,-1,0,1,-1,0,1
1,0,1,1,1,1,0,-1,0,-1
1,0,1,0,0,0,0,1,1,-1
-1,0,1,0,-1,-1,0,1,0,-1
-1,-1,-1,0,-1,1,0,-1,0,1
1,1,1,0,-1,0,1,-1,0,-1
1,-1,0,-1,-1,0,0,-1,0,1
1,0,1,-1,1,-1,0,1,0,-1
-1,-1,0,-1,-1,-1,0,1,0,0
0,0,-1,0,1,1,0,-1,0,1
0,-1,-1,0,1,0,0,-1,0,1
1,0,1,-1,-1,1,1,-1,0,-1
1,0,1,-1,1,-1,0,1,1,-1
1,0,1,-1,1,0,-1,1,1,-1
0,0,0,0,1,0,0,-1,0,1
1,0,1,1,1,-1,-1,1,0,-1
1,0,1,0,-1,0,1,1,0,-1
-1,0,1,0,-1,0,-1,1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,1,0,-1,0,0,1,0,-1
-1,0,-1,-1,-1,-1,0,1,0,1
1,1,-1,-1,-1,1,1,-1,0,-1
1,0,1,1,1,-1,0,1,0,-1
1,0,1,0,-1,1,1,-1,0,-1
-1,-1,0,-1,-1,1,-1,-1,0,1
1,1,1,-1,1,-1,0,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
-1,-1,-1,0,-1,0,0,1,0,1
-1,-1,0,-1,1,-1,0,1,0,1
-1,0,1,0,0,-1,1,1,0,-1
1,1,1,-1,1,0,-1,1,1,-1
1,0,1,0,-1,1,-1,-1,0,-1
1,1,1,0,0,1,-1,-1,0,-1
1,1,1,1,1,-1,1,1,0,-1
-1,-1,1,-1,-1,0,0,-1,0,1
0,0,-1,0,1,-1,-1,1,0,1
1,1,1,1,1,-1,0,1,1,-1
-1,-1,1,-1,0,1,-1,-1,0,-1
1,1,1,-1,-1,-1,1,1,0,0
1,0,1,1,0,-1,1,1,0,-1
-1,-1,-1,1,-1,1,1,-1,1,1
-1,0,0,-1,-1,-1,0,1,0,1
0,-1,-1,0,1,-1,1,1,0,1
-1,1,1,0,0,1,0,-1,0,-1
0,0,-1,0,1,1,0,-1,0,1
-1,0,-1,-1,-1,0,1,-1,0,1
-1,-1,1,-1,-1,0,0,-1,0,1
1,0,1,-1,1,-1,0,1,0,-1
1,1,1,0,0,-1,0,1,0,-1
1,-1,1,-1,-1,0,-1,1,0,-1
-1,-1,1,1,-1,1,-1,1,0,1
-1,-1,1,-1,-1,1,-1,-1,0,1
-1,0,1,0,0,-1,0,1,0,-1
1,-1,0,-1,1,-1,1,1,0,1
1,-1,1,1,1,0,1,1,0,-1
1,0,1,0,-1,0,-1,1,0,-1
1,-1,1,-1,1,-1,0,1,0,-1
1,-1,0,0,1,-1,-1,1,0,1
1,0,1,1,1,0,0,-1,0,-1
-1,-1,-1,-1,-1,0,1,-1,0,1
-1,-1,1,-1,0,0,-1,-1,0,1
1,0,1,0,-1,0,0,1,0,-1
0,-1,-1,0,0,-1,0,1,0,1
1,0,1,-1,1,0,-1,-1,0,-1
-1,0,-1,-1,-1,1,0,-1,0,1
-1,0,-1,-1,1,0,-1,-1,0,1
-1,0,-1,0,-1,1,-1,-1,0,1
1,0,1,-1,1,0,1,-1,0,-1
-1,-1,0,1,1,-1,1,1,0,1
1,0,1,1,0,0,1,1,0,-1
1,1,1,0,-1,0,-1,1,1,-1
1,0,1,-1,-1,0,0,-1,0,-1
1,1,1,0,0,1,1,-1,0,-1
1,-1,0,-1,1,-1,0,1,0,1
-1,-1,0,-1,-1,0,0,1,0,1
-1,-1,-1,-1,-1,1,0,1,0,1
-1,0,0,-1,-1,0,0,1,0,1
1,-1,1,-1,1,0,-1,1,0,1
1,0,0,1,-1,-1,0,1,0,-1
1,-1,0,-1,-1,0,-1,1,0,1
-1,-1,-1,-1,1,1,1,-1,0,1
1,-1,0,1,-1,-1,0,1,0,-1
-1,-1,0,0,-1,0,-1,1,0,1
-1,0,1,0,0,-1,0,1,1,-1
-1,-1,0,-1,-1,1,0,1,0,1
1,0,1,0,-1,1,1,-1,0,-1
1,0,1,1,0,1,1,-1,1,-1
0,0,-1,0,1,0,0,1,0,1
1,0,1,0,-1,1,0,-1,0,-1
1,-1,1,-1,1,0,0,1,0,-1
-1,0,0,0,-1,0,-1,1,0,1
-1,-1,0,-1,-1,0,-1,-1,0,1
-1,-1,-1,0,-1,-1,0,1,0,1
1,-1,1,0,-1,-1,1,1,0,-1
1,1,1,1,1,1,1,-1,0,-1
1,1,-1,-1,1,-1,0,1,0,-1
1,-1,1,0,1,0,-1,1,0,1
-1,-1,0,0,-1,1,-1,-1,0,1
1,0,1,-1,1,0,1,1,0,-1
0,-1,-1,0,1,1,0,-1,0,1
1,-1,1,-1,0,1,-1,-1,0,0
0,-1,-1,0,1,-1,0,1,0,1
1,1,-1,1,1,-1,0,1,0,-1
-1,-1,1,0,1,1,1,-1,0,1
1,0,1,0,-1,1,1,-1,0,-1
-1,0,-1,-1,-1,0,0,-1,0,1
0,-1,-1,0,1,0,0,1,0,1
-1,0,0,-1,-1,-1,-1,1,0,0
-1,-1,-1,-1,-1,0,-1,1,0,1
1,0,-1,0,0,1,-1,-1,0,-1
1,1,1,1,1,-1,1,1,1,-1
-1,-1,-1,-1,-1,-1,-1,1,0,1
1,0,1,1,1,0,0,1,0,-1
-1,0,-1,0,-1,0,1,1,0,1
-1,0,-1,1,1,-1,1,1,0,-1
1,1,1,0,0,-1,1,1,1,-1
-1,0,-1,-1,1,1,0,-1,0,1
1,0,-1,-1,-1,1,0,-1,0,-1
0,0,-1,0,0,1,-1,-1,0,1
1,-1,0,0,-1,1,-1,1,0,1
-1,-1,-1,-1,1,1,0,-1,0,1
1,1,1,-1,-1,1,1,-1,0,-1
0,-1,-1,0,0,0,0,1,0,1
1,0,1,-1,1,0,-1,1,1,-1
-1,-1,-1,0,-1,1,0,1,0,1
0,-1,-1,0,0,0,0,-1,0,1
-1,0,-1,-1,-1,0,0,1,0,1
1,0,1,-1,1,-1,1,1,0,-1
1,-1,-1,-1,1,1,-1,-1,0,-1
1,0,1,0,-1,0,0,1,0,-1
-1,-1,-1,0,-1,-1,0,1,0,1
1,1,1,-1,1,1,1,-1,0,-1
-1,-1,-1,-1,-1,1,0,-1,0,1
-1,-1,-1,-1,-1,0,0,-1,0,1
1,-1,1,1,1,-1,0,1,0,-1
-1,0,0,0,-1,1,-1,-1,0,1
1,-1,0,0,1,-1,0,1,0,-1
1,1,-1,-1,1,1,1,-1,0,0
1,0,1,-1,1,-1,1,1,0,-1
1,1,1,0,0,-1,0,1,1,-1
-1,-1,1,-1,-1,0,1,1,1,1
1,0,1,0,-1,1,1,-1,0,-1
1,1,0,0,-1,0,-1,1,0,-1
1,0,1,1,0,-1,1,1,0,-1
1,0,1,-1,1,0,0,1,0,-1
-1,-1,1,-1,-1,1,-1,-1,0,1
1,1,1,-1,-1,0,0,1,0,-1
1,0,1,1,1,0,1,1,0,-1
-1,0,0,-1,-1,-1,0,1,0,0
1,-1,1,-1,-1,0,-1,1,0,1
-1,-1,-1,0,-1,1,0,-1,0,1
1,0,-1,-1,1,1,1,-1,0,0
1,0,1,0,-1,0,0,1,0,-1
1,0,-1,-1,1,0,1,1,0,0
1,1,1,0,0,-1,0,1,0,-1
1,-1,1,1,1,-1,1,1,0,-1
-1,-1,0,-1,-1,0,0,-1,0,1
-1,0,0,0,-1,1,-1,-1,0,1
-1,0,-1,-1,-1,0,-1,-1,0,1
-1,-1,1,-1,-1,-1,-1,1,0,1
-1,-1,1,-1,1,1,0,1,0,0
0,-1,-1,0,1,1,1,-1,1,1
-1,-1,1,0,-1,-1,0,1,0,1
-1,0,1,-1,0,0,-1,1,0,1
1,-1,1,0,-1,1,1,-1,0,-1
1,1,1,-1,-1,0,-1,1,0,-1
-1,0,1,0,-1,0,1,1,1,1
0,0,-1,0,1,0,1,-1,1,1
0,0,-1,0,1,-1,0,1,0,1
-1,-1,-1,-1,-1,0,0,1,0,1
1,0,1,-1,-1,-1,1,1,0,0
-1,0,-1,-1,-1,1,0,-1,0,1
1,0,0,1,1,-1,-1,1,0,-1
-1,-1,1,-1,-1,0,1,1,0,0
-1,0,1,-1,-1,0,-1,-1,0,1
-1,-1,-1,-1,-1,0,0,-1,0,1
1,0,1,0,0,0,-1,1,0,-1
-1,-1,-1,-1,-1,0,0,1,0,1
1,-1,1,0,-1,-1,0,1,0,-1
1,-1,1,-1,-1,1,-1,-1,0,1
-1,-1,1,-1,1,1,0,-1,0,0
-1,-1,-1,-1,-1,1,0,-1,0,1
-1,-1,-1,0,-1,-1,1,1,1,1
1,1,1,-1,-1,-1,0,1,0,-1
1,0,1,1,0,-1,-1,1,1,-1
-1,-1,1,-1,-1,0,-1,1,0,1
1,1,1,0,-1,1,-1,-1,0,-1
1,0,1,1,1,0,1,1,0,-1
-1,-1,-1,0,-1,-1,1,1,1,1
0,0,-1,0,0,-1,0,1,0,1
1,0,1,-1,-1,0,0,1,0,-1
1,0,1,0,-1,0,0,1,0,-1
1,0,-1,0,-1,1,0,-1,0,-1
1,-1,0,-1,-1,0,-1,-1,0,1
-1,0,-1,0,-1,0,0,1,0,1
-1,-1,1,-1,-1,-1,0,1,0,1
-1,0,1,1,0,1,-1,-1,0,-1
1,0,1,-1,-1,-1,0,1,0,-1
1,0,1,0,0,-1,0,1,0,-1
-1,-1,0,-1,-1,1,0,-1,0,1
-1,-1,-1,-1,-1,0,1,-1,1,1
1,0,1,-1,1,0,1,1,0,-1
0,0,-1,0,1,0,-1,-1,0,1
0,0,1,0,0,-1,1,1,1,1
1,-1,1,-1,-1,1,1,-1,0,0
-1,-1,1,-1,1,1,0,-1,0,0
-1,-1,1,0,1,1,1,-1,0,1
1,0,1,0,-1,-1,-1,1,1,-1
1,0,1,0,-1,0,0,1,0,-1
1,0,1,-1,1,0,1,-1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,-1,1,-1,1,1,1,-1,0,-1
0,-1,0,0,1,1,0,-1,0,1
1,1,1,-1,1,-1,0,1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
1,1,1,1,1,-1,-1,1,0,-1
1,-1,0,0,-1,1,-1,-1,0,1
-1,-1,1,-1,0,1,-1,-1,0,1
-1,-1,1,0,-1,-1,1,1,1,1
-1,-1,0,-1,-1,0,-1,1,0,-1
1,0,1,0,-1,1,1,-1,0,-1
-1,-1,1,-1,0,0,-1,1,0,1
-1,0,1,-1,-1,0,-1,1,0,1
-1,0,0,-1,-1,1,-1,-1,0,1
1,-1,0,1,1,-1,-1,1,1,1
1,-1,-1,-1,1,-1,1,1,0,0
-1,-1,-1,0,-1,0,0,1,0,1
1,0,1,1,1,0,0,-1,0,-1
0,-1,0,0,1,0,0,1,0,1
1,-1,0,1,1,0,1,1,0,1
-1,-1,1,-1,-1,0,-1,-1,0,-1
1,-1,0,0,0,-1,1,1,0,-1
1,0,1,0,-1,-1,-1,1,1,-1
1,0,1,1,0,-1,-1,1,1,-1
-1,-1,-1,-1,-1,0,1,1,0,1
-1,1,1,1,1,-1,0,1,1,-1
1,0,0,1,1,1,0,-1,0,-1
1,0,1,-1,-1,-1,1,1,0,-1
1,1,1,1,0,1,1,-1,0,-1
-1,-1,1,-1,1,1,-1,-1,0,0
1,0,1,0,-1,-1,0,1,0,-1
1,0,1,1,1,0,0,-1,0,-1
1,1,1,-1,1,0,-1,1,0,-1
1,0,1,0,-1,-1,1,1,0,-1
-1,-1,-1,-1,-1,0,-1,1,0,1
1,1,1,1,1,-1,0,1,0,-1
1,0,1,1,0,-1,0,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
-1,0,-1,-1,-1,0,-1,-1,0,1
0,0,-1,0,1,1,1,-1,0,1
1,0,1,-1,1,1,0,-1,0,-1
1,-1,0,1,0,0,-1,-1,0,-1
-1,0,1,0,-1,0,0,1,0,-1
-1,-1,1,-1,-1,0,0,1,0,1
1,0,1,-1,1,-1,1,1,1,-1
1,-1,0,1,1,-1,-1,1,1,-1
1,0,1,1,1,-1,0,1,1,-1
1,0,1,0,-1,0,0,-1,0,-1
-1,-1,1,0,-1,0,0,1,0,1
-1,-1,0,-1,-1,0,-1,-1,0,1
1,1,1,-1,-1,0,0,1,1,-1
-1,0,-1,-1,-1,1,1,-1,1,1
1,0,-1,1,1,0,1,1,0,-1
1,0,1,-1,1,-1,0,1,0,-1
-1,-1,0,0,-1,-1,-1,1,0,0
1,1,1,-1,-1,1,1,-1,0,0
1,0,1,1,1,-1,-1,1,0,-1
0,-1,0,0,0,0,-1,1,0,0
-1,-1,-1,0,-1,0,1,1,0,1
-1,-1,1,0,-1,0,1,1,0,1
1,0,-1,1,1,1,1,-1,0,-1
1,0,1,-1,1,0,1,-1,0,-1
0,-1,-1,0,1,-1,-1,1,0,1
1,0,1,-1,1,-1,-1,1,0,-1
1,0,1,-1,-1,0,1,1,0,0
1,1,1,-1,1,-1,0,1,0,-1
1,0,1,-1,-1,-1,1,1,1,0
-1,-1,1,-1,-1,-1,1,1,0,0
-1,-1,-1,0,1,0,1,1,1,1
1,0,1,-1,1,-1,1,1,0,-1
-1,0,-1,-1,1,1,-1,-1,0,1
0,-1,0,0,1,1,1,-1,1,1
1,1,-1,1,1,0,0,1,0,-1
1,-1,0,1,1,-1,1,1,0,1
1,0,1,0,-1,-1,1,1,0,-1
1,1,-1,1,1,1,-1,-1,0,-1
1,0,-1,-1,-1,1,1,1,0,-1
-1,0,1,1,1,-1,1,1,0,-1
1,0,0,0,-1,-1,-1,1,0,-1
1,-1,0,-1,-1,-1,-1,1,0,1
-1,-1,0,-1,-1,1,0,-1,0,1
1,0,1,0,-1,-1,1,1,0,-1
1,0,1,-1,1,0,0,1,1,-1
-1,-1,-1,0,-1,0,0,1,0,1
-1,-1,0,-1,-1,1,0,-1,0,1
1,1,1,-1,1,-1,-1,1,0,-1
-1,-1,1,-1,-1,1,-1,-1,0,1
0,-1,-1,0,1,0,0,1,0,1
1,0,-1,1,1,-1,0,1,0,-1
1,0,1,1,1,1,1,-1,0,-1
-1,0,0,0,-1,-1,-1,1,0,0
1,-1,0,-1,-1,1,0,-1,0,1
-1,0,1,-1,-1,1,-1,-1,0,1
0,-1,1,0,0,-1,-1,1,0,1
1,1,-1,1,1,-1,1,1,0,-1
1,-1,1,1,1,1,1,-1,0,-1
1,0,1,-1,1,0,1,1,1,-1
0,0,-1,0,0,1,1,-1,0,1
1,0,-1,0,-1,1,0,-1,0,-1
0,-1,1,0,1,1,-1,-1,0,1
-1,-1,0,-1,-1,0,0,-1,0,1
-1,0,1,0,-1,1,-1,-1,0,1
0,0,-1,0,0,1,0,-1,0,1
1,0,1,0,-1,0,1,1,0,-1
-1,0,-1,-1,1,1,-1,1,0,1
1,0,1,0,-1,0,0,1,0,-1
-1,0,1,-1,-1,1,0,1,0,-1
1,1,-1,1,1,1,1,-1,0,-1
0,0,0,0,1,0,0,-1,0,1
-1,0,1,-1,1,0,-1,1,0,0
1,1,0,0,-1,-1,-1,1,0,-1
1,0,1,1,1,-1,0,1,0,-1
-1,-1,-1,-1,-1,-1,0,1,0,1
-1,0,1,0,-1,0,0,1,0,-1
-1,0,-1,-1,-1,0,-1,-1,0,1
0,0,1,0,0,0,-1,1,0,1
1,0,1,1,1,0,-1,-1,0,-1
