%pip install liac-arff pymining

%pip install --upgrade pip setuptools
%pip install liac-arff pymining


import arff
import pandas as pd
import time
from collections import defaultdict
from itertools import combinations
from pymining import seqmining

with open('/Users/<USER>/Desktop/untitled folder/PhishingData.arff ') as f:
    data = arff.load(f)
df = pd.DataFrame(data['data'], columns=[attr[0] for attr in data['attributes']])

def discretize_row(row):
    return [f"{col}_{'neg' if v == -1 else 'zero' if v == 0 else 'pos'}"
            for col, v in zip(df.columns[:-1], row[:-1])]

sequences = df.apply(discretize_row, axis=1).tolist()

print(" Sample Sequence:")
for i, item in enumerate(sequences[0][:10]):
    print(f"  {i+1}. {item}")


results = []

def run_gsp(sequences, min_support=3):
    start = time.time()
    patterns = list(seqmining.freq_seq_enum(sequences, min_support))
    return patterns, time.time() - start

print("\n--- Running Simplified GSP for different min_support values ---")
print("\nSample sequence (Simplified GSP - first 5 itemsets):")
for i, item in enumerate(sequences[0][:10]):
    print(f"  {i+1}. {list(item)}")

for min_sup in [3, 4, 5]:
    print(f"\n--- Running Simplified GSP with min_support={min_sup} ---")
    gsp_patterns, gsp_time = run_gsp(sequences, min_support=min_sup)
    print(f"Simplified GSP completed in {gsp_time:.4f} seconds.")
    print(f"Simplified GSP with min_support={min_sup} completed. Patterns found: {len(gsp_patterns)}, Runtime: {gsp_time:.4f} seconds.")

    results.append({
        "Algorithm": "GSP (Simplified)",
        "Min Support": min_sup,
        "Patterns Found": len(gsp_patterns),
        "Runtime (s)": round(gsp_time, 6)
    })


def run_prefixspan(sequences, min_support=3):
    def mine(seqs, prefix=[]):
        results = []
        items = defaultdict(int)
        for seq in seqs:
            counted = set()
            for item in seq:
                if item not in counted:
                    items[item] += 1
                    counted.add(item)
        for item, count in items.items():
            if count >= min_support:
                new_prefix = prefix + [item]
                results.append((count, new_prefix))
                projected = [s[s.index(item)+1:] for s in seqs if item in s]
                results.extend(mine(projected, new_prefix))
        return results
    start = time.time()
    patterns = mine(sequences)
    return patterns, time.time() - start


print("\n--- Running PrefixSpan for different min_support values ---")
print("\nSample sequence (PrefixSpan - first 5 itemsets):")
for i, item in enumerate(sequences[0][:10]):
    print(f"  {i+1}. {list(item)}")

for min_sup in [3, 4, 5]:
    print(f"\n--- Running PrefixSpan with min_support={min_sup} ---")
    ps_patterns, ps_time = run_prefixspan(sequences, min_support=min_sup)
    print(f"PrefixSpan completed in {ps_time:.4f} seconds.")
    print(f"PrefixSpan with min_support={min_sup} completed. Patterns found: {len(ps_patterns)}, Runtime: {ps_time:.4f} seconds.")

    results.append({
        "Algorithm": "PrefixSpan",
        "Min Support": min_sup,
        "Patterns Found": len(ps_patterns),
        "Runtime (s)": round(ps_time, 6)
    })


def run_spade_simplified(sequences, min_support=3):
    start = time.time()
    vertical = defaultdict(list)
    for sid, seq in enumerate(sequences):
        for t, item in enumerate(seq):
            vertical[item].append((sid, t))
    freq1 = { (item,): occs for item, occs in vertical.items()
              if len(set(sid for sid, _ in occs)) >= min_support }
    all_patterns = {k: len(set(sid for sid, _ in v)) for k, v in freq1.items()}
    current = freq1
    while current:
        next_level = defaultdict(list)
        for s1 in current:
            for s2 in freq1:
                candidate = s1 + s2
                occ1 = current[s1]
                occ2 = freq1[s2]
                joined = [(sid1, t2) for sid1, t1 in occ1
                          for sid2, t2 in occ2 if sid1 == sid2 and t2 > t1]
                if len(set(sid for sid, _ in joined)) >= min_support:
                    next_level[candidate] = joined
                    all_patterns[candidate] = len(set(sid for sid, _ in joined))
        current = next_level
    return [(v, [[i] for i in p]) for p, v in all_patterns.items()], time.time() - start

def is_subsequence(subseq, seq):
    it = iter(seq)
    return all(item in it for item in subseq)

print("\n--- Running Simplified SPADE for different min_support values ---")
print("\nSample sequence (Simplified SPADE - first 5 itemsets):")
for i, item in enumerate(sequences[0][:10]):
    print(f"  {i+1}. {list(item)}")

for min_sup in [3, 4, 5]:
    print(f"\n--- Running Simplified SPADE with min_support={min_sup} ---")
    spade_patterns, spade_time = run_spade_simplified(sequences, min_support=min_sup)
    print(f"Simplified SPADE completed in {spade_time:.4f} seconds.")
    print(f"Simplified SPADE with min_support={min_sup} completed. Patterns found: {len(spade_patterns)}, Runtime: {spade_time:.4f} seconds.")

    results.append({
        "Algorithm": "SPADE (Simplified)",
        "Min Support": min_sup,
        "Patterns Found": len(spade_patterns),
        "Runtime (s)": round(spade_time, 6)
    })


def run_apriori_all(sequences, min_support=3, max_len=3):
    start = time.time()
    item_counts = defaultdict(int)
    for seq in sequences:
        for item in seq:
            item_counts[(item,)] += 1
    freq_patterns = {k: v for k, v in item_counts.items() if v >= min_support}
    all_patterns = dict(freq_patterns)
    current_Lk = list(freq_patterns.keys())
    k = 2
    while current_Lk and k <= max_len:
        candidate_counts = defaultdict(int)
        for i in range(len(current_Lk)):
            for j in range(len(current_Lk)):
                a, b = current_Lk[i], current_Lk[j]
                if a[1:] == b[:-1]:
                    candidate = a + (b[-1],)
                    for seq in sequences:
                        if is_subsequence(candidate, seq):
                            candidate_counts[candidate] += 1
        current_Lk = [k for k, v in candidate_counts.items() if v >= min_support]
        all_patterns.update({k: v for k, v in candidate_counts.items() if v >= min_support})
        k += 1
    return [(v, [[item for item in k]]) for k, v in all_patterns.items()], time.time() - start

print("\n--- Running Simplified AprioriAll for different min_support values ---")
print("\nSample sequence (Simplified AprioriAll - first 5 itemsets):")
for i, item in enumerate(sequences[0][:10]):
    print(f"  {i+1}. {list(item)}")

for min_sup in [3, 4, 5]:
    print(f"\n--- Running Simplified AprioriAll with min_support={min_sup} ---")
    apriori_patterns, apriori_time = run_apriori_all(sequences, min_support=min_sup)
    print(f"Simplified AprioriAll completed in {apriori_time:.4f} seconds.")
    print(f"Simplified AprioriAll with min_support={min_sup} completed. Patterns found: {len(apriori_patterns)}, Runtime: {apriori_time:.4f} seconds.")

    results.append({
        "Algorithm": "AprioriAll (Simplified)",
        "Min Support": min_sup,
        "Patterns Found": len(apriori_patterns),
        "Runtime (s)": round(apriori_time, 6)
    })


import pandas as pd
if results: 
    df_perf = pd.DataFrame(results)

    print("\n--- Algorithm Performance Metrics ---")
    display(df_perf)

    # Best and Worst Performer per min_support
    print("\n--- Best and Worst Performers (Based on Runtime) ---")
    for support in sorted(df_perf['Min Support'].unique()):
        subset = df_perf[df_perf['Min Support'] == support]
        best = subset.loc[subset['Runtime (s)'].idxmin()]
        worst = subset.loc[subset['Runtime (s)'].idxmax()]

        print(f"\nMin Support = {support}:")
        print(f"  Best Performer: {best['Algorithm']} (Runtime: {best['Runtime (s)']:.4f} s)")
        print(f"  Worst Performer: {worst['Algorithm']} (Runtime: {worst['Runtime (s)']:.4f} s)")
else:
    print(" The 'results' list is empty. Cannot generate performance metrics.")